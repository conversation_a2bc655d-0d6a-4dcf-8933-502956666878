import React, { useState, useEffect } from 'react'
import { Send, Users, Calendar, FileText, Smartphone, Plus, X, Edit2, Phone, Upload, BarChart3, Trash2, ShoppingCart } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useNotification } from '../../contexts/NotificationContext'
import { messageApi, groupApi, deviceApi } from '../../lib/api'
import { cn } from '../../lib/utils'
import ConfirmationModal from '../../components/ConfirmationModal'

interface Group {
  id: string
  name: string
  contact_count?: number
}

interface Device {
  j_id: string
  registration_id: string
  platform: string
  push_name: string
  business_name: string
  device_number: string
  state: string // 1- online 2- çıkış
}

interface Receiver {
  id: string
  countryCode: string
  phoneNumber: string
  fullNumber: string
}

export const SendMessage: React.FC = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { showSuccess, showError } = useNotification()
  const [content, setContent] = useState('')
  const [receiversList, setReceiversList] = useState<Receiver[]>([])
  const [currentCountryCode, setCurrentCountryCode] = useState('+90')
  const [currentPhoneNumber, setCurrentPhoneNumber] = useState('')
  const [editingReceiver, setEditingReceiver] = useState<string | null>(null)
  const [sendTime, setSendTime] = useState('')
  const [format, setFormat] = useState('1') // 1-text, 2-media, 3-poll
  const [groups, setGroups] = useState<Group[]>([])
  const [devices, setDevices] = useState<Device[]>([])
  const [selectedDevice, setSelectedDevice] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [usedGroupIds, setUsedGroupIds] = useState<Set<string>>(new Set())

  // Statistics state
  const [sentToday, setSentToday] = useState<number>(0)
  const [scheduled, setScheduled] = useState<number>(0)

  // Credit modal state
  const [showCreditModal, setShowCreditModal] = useState(false)

  // Media upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // Poll state
  const [pollOptions, setPollOptions] = useState<string[]>(['', ''])
  const [selectableOptionCount, setSelectableOptionCount] = useState(1)

  useEffect(() => {
    const abortController = new AbortController()

    fetchGroups(abortController.signal)
    fetchDevices(abortController.signal)
    fetchStatistics(abortController.signal)

    return () => {
      abortController.abort()
    }
  }, [])

  // Format değiştiğinde ilgili state'leri temizle
  useEffect(() => {
    if (format === '1') {
      // Text format - media ve poll state'lerini temizle
      setSelectedFile(null)
      setPollOptions(['', ''])
      setSelectableOptionCount(1)
    } else if (format === '2') {
      // Media format - poll state'lerini temizle
      setPollOptions(['', ''])
      setSelectableOptionCount(1)
    } else if (format === '3') {
      // Poll format - media state'lerini temizle
      setSelectedFile(null)
    }
  }, [format])

  const fetchGroups = async (signal?: AbortSignal) => {
    try {
      const data = await groupApi.getGroupsWithStats()
      if (signal?.aborted) return
      setGroups(data.groups || [])
    } catch (err) {
      if (signal?.aborted) return
      console.error('Error fetching groups:', err)
      setGroups([])
    }
  }

  const fetchDevices = async (signal?: AbortSignal) => {
    try {
      const data = await deviceApi.getDevices()
      if (signal?.aborted) return
      setDevices(data || [])
      // İlk cihazı otomatik seç
      if (data && data.length > 0) {
        setSelectedDevice(data[0].registration_id)
      }
    } catch (err) {
      if (signal?.aborted) return
      console.error('Error fetching devices:', err)
      setDevices([])
    }
  }

  const fetchStatistics = async (signal?: AbortSignal) => {
    try {
      // Bugün için tarih aralığı oluştur
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

      // Sent Today için status 3 olan mesajları getir (bugün gönderilen)
      const sentTodayData = await messageApi.getDashboardStats({
        status: '3',
        initial_time: startOfDay.toISOString(),
        end_time: endOfDay.toISOString()
      })

      // Scheduled için status 1 olan mesajları getir (bekleyen)
      const scheduledData = await messageApi.getDashboardStats({
        status: '1'
      })

      if (signal?.aborted) return

      setSentToday(sentTodayData.success_messages || 0)
      setScheduled(scheduledData.processing_messages || 0)
    } catch (err) {
      if (signal?.aborted) return
      console.error('Error fetching statistics:', err)
      setSentToday(0)
      setScheduled(0)
    }
  }

  // Receiver yönetimi fonksiyonları
  const generateReceiverId = () => {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11)
  }

  const validatePhoneNumber = (phoneNumber: string) => {
    // Basit telefon numarası validasyonu
    const phoneRegex = /^[0-9]{7,15}$/
    return phoneRegex.test(phoneNumber.replace(/\s/g, ''))
  }

  const addReceiver = () => {
    if (!currentPhoneNumber.trim()) {
      showError(t('messages.phoneNumberRequired'))
      return
    }

    const cleanPhoneNumber = currentPhoneNumber.replace(/\s/g, '')

    if (!validatePhoneNumber(cleanPhoneNumber)) {
      showError(t('messages.invalidPhoneNumber'))
      return
    }

    const fullNumber = currentCountryCode + cleanPhoneNumber

    // Aynı numaranın zaten eklenip eklenmediğini kontrol et
    if (receiversList.some(r => r.fullNumber === fullNumber)) {
      showError(t('messages.receiverExists'))
      return
    }

    const newReceiver: Receiver = {
      id: generateReceiverId(),
      countryCode: currentCountryCode,
      phoneNumber: cleanPhoneNumber,
      fullNumber: fullNumber
    }

    setReceiversList(prev => [...prev, newReceiver])
    setCurrentPhoneNumber('')
  }

  const removeReceiver = (id: string) => {
    setReceiversList(prev => prev.filter(r => r.id !== id))
  }

  const startEditReceiver = (receiver: Receiver) => {
    setEditingReceiver(receiver.id)
    setCurrentCountryCode(receiver.countryCode)
    setCurrentPhoneNumber(receiver.phoneNumber)
  }

  const saveEditReceiver = () => {
    if (!editingReceiver) return

    if (!currentPhoneNumber.trim()) {
      showError(t('messages.phoneNumberRequired'))
      return
    }

    const cleanPhoneNumber = currentPhoneNumber.replace(/\s/g, '')

    if (!validatePhoneNumber(cleanPhoneNumber)) {
      showError(t('messages.invalidPhoneNumber'))
      return
    }

    const fullNumber = currentCountryCode + cleanPhoneNumber

    // Aynı numaranın başka bir receiver'da olup olmadığını kontrol et
    if (receiversList.some(r => r.id !== editingReceiver && r.fullNumber === fullNumber)) {
      showError(t('messages.receiverExists'))
      return
    }

    setReceiversList(prev => prev.map(r =>
      r.id === editingReceiver
        ? { ...r, countryCode: currentCountryCode, phoneNumber: cleanPhoneNumber, fullNumber: fullNumber }
        : r
    ))

    setEditingReceiver(null)
    setCurrentPhoneNumber('')
    setCurrentCountryCode('+90')
  }

  const cancelEditReceiver = () => {
    setEditingReceiver(null)
    setCurrentPhoneNumber('')
    setCurrentCountryCode('+90')
  }

  // Poll helper functions
  const addPollOption = () => {
    setPollOptions(prev => [...prev, ''])
  }

  const removePollOption = (index: number) => {
    if (pollOptions.length > 2) {
      setPollOptions(prev => prev.filter((_, i) => i !== index))
    }
  }

  const updatePollOption = (index: number, value: string) => {
    setPollOptions(prev => prev.map((option, i) => i === index ? value : option))
  }

  // File upload handler
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (receiversList.length === 0) {
        throw new Error(t('messages.validation.receiversRequired') ?? 'Receivers are required')
      }

      if (!selectedDevice) {
        throw new Error(t('messages.validation.deviceRequired') ?? 'Device is required')
      }

      // Media validation
      if (format === '2' && !selectedFile) {
        throw new Error(t('messages.fileRequired') ?? 'File is required')
      }

      // Poll validation
      if (format === '3') {
        const validPollOptions = pollOptions.filter(option => option.trim() !== '')
        if (validPollOptions.length < 2) {
          throw new Error(t('messages.pollOptionsRequired') ?? 'Poll options are required')
        }
      }

      // Receiver'ları virgülle birleştir
      const receiversArray = receiversList.map(r => r.fullNumber)

      // FormData oluştur
      const formData = new FormData()
      formData.append('content', content)

      // Receivers'ı ayrı ayrı ekle
      receiversArray.forEach(receiver => {
        formData.append('receivers', receiver)
      })

      formData.append('send_time', sendTime || new Date().toISOString())
      formData.append('format', format)
      formData.append('reg_id', selectedDevice)

      // Media için dosya ekle
      if (format === '2' && selectedFile) {
        formData.append('file', selectedFile)
      }

      // Poll için seçenekleri ekle
      if (format === '3') {
        const validPollOptions = pollOptions.filter(option => option.trim() !== '')
        validPollOptions.forEach(option => {
          formData.append('poll_option', option)
        })
        formData.append('selectable_option_count', selectableOptionCount.toString())
      }

      await messageApi.sendMessage(formData)
      showSuccess(t('notifications.operationSuccess'))

      // İstatistikleri yenile
      fetchStatistics()

      // Form'u temizle
      setContent('')
      setReceiversList([])
      setSendTime('')
      setUsedGroupIds(new Set())
      setSelectedFile(null)
      setPollOptions(['', ''])
      setSelectableOptionCount(1)

    } catch (err: any) {
      // Extract error message from different possible response formats
      let errorMessage = t('notifications.operationError')

      if (err.response?.data) {
        // If response.data is a string (direct error message)
        if (typeof err.response.data === 'string') {
          errorMessage = err.response.data
        }
        // If response.data is an object with message property
        else if (err.response.data.message) {
          errorMessage = err.response.data.message
        }
        // If response.data is an object but no message property, try to stringify
        else if (typeof err.response.data === 'object') {
          errorMessage = JSON.stringify(err.response.data)
        }
      } else if (err.message) {
        errorMessage = err.message
      }

      // Check for insufficient credit error
      if (errorMessage.toLowerCase().includes('insufficient credit')) {
        showError(t('messages.insufficientCredit'))
        setShowCreditModal(true)
      } else {
        showError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoToPackages = () => {
    setShowCreditModal(false)
    navigate('/packages')
  }

  const handleGroupSelect = async (groupId: string) => {
    if (!groupId) return

    try {
      // Grup contact'larını al
      const contacts = await groupApi.getGroupContacts(groupId)

      // Contact'ları receiver formatına çevir - backend'den country_code ve number ayrı geliyor
      const newReceivers: Receiver[] = contacts.map((contact: any) => {
        // Backend'den country_code ve number ayrı geliyor
        const countryCode = contact.country_code ? `+${contact.country_code}` : ''
        const phoneNumber = contact.number

        // Full number'ı oluştur
        const fullNumber = countryCode ? `${countryCode}${phoneNumber}` : phoneNumber

        return {
          id: `${Date.now()}-${Math.random()}`,
          countryCode,
          phoneNumber,
          fullNumber
        }
      })

      // Mevcut alıcılar listesine ekle (duplicate kontrolü ile)
      const existingNumbers = receiversList.map(r => r.fullNumber)
      const uniqueReceivers = newReceivers.filter(r => !existingNumbers.includes(r.fullNumber))

      if (uniqueReceivers.length > 0) {
        setReceiversList(prev => [...prev, ...uniqueReceivers])
        showSuccess(`${uniqueReceivers.length} ${t('messages.contactsAdded') || 'contacts added from group'}`)

        // Seçilen grubu kullanılmış gruplar listesine ekle
        setUsedGroupIds(prev => {
          const newSet = new Set(prev)
          newSet.add(groupId)
          return newSet
        })
      } else {
        showError(t('messages.allContactsAlreadyAdded') || 'All contacts from this group are already added')
      }
    } catch (err: any) {
      showError(t('notifications.loadError'))
      console.error('Error loading group contacts:', err)
    }
  }

  return (
    <div className="h-full bg-gray-900 text-white">
      <div className="h-full px-4 md:px-6 py-4 md:py-8 space-y-6 md:space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
          <div className="space-y-1 md:space-y-2">
            <h1 className="text-2xl md:text-3xl font-bold text-emerald-400">
              {t('navigation.sendMessage')}
            </h1>
            <p className="text-gray-400 text-base md:text-lg">
              {t('messages.sendDescription')}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2 bg-gray-800 px-3 md:px-4 py-2 rounded-lg">
              <Send className="h-4 w-4 text-emerald-400" />
              <span className="text-sm text-gray-300">{t('navigation.sendMessage')}</span>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-4 md:p-6">
        <form onSubmit={handleSubmit} className="space-y-4 md:space-y-6">
          {/* Group Selection */}
          {groups && groups.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                {t('messages.groupSelection')} ({t('common.optional')})
              </label>
              <div className="space-y-2">
                <p className="text-sm text-gray-400 mb-3">
                  {t('messages.selectGroupToAddContacts') || 'Select a group to add all its contacts to receivers list'}
                </p>
                <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
                  {(() => {
                    const availableGroups = groups.filter(group =>
                      (group.contact_count || 0) > 0 && // Sadece contact'ı olan gruplar
                      !usedGroupIds.has(group.id) // Henüz kullanılmamış gruplar
                    )

                    if (availableGroups.length === 0) {
                      return (
                        <div className="text-center py-4">
                          <Users className="h-8 w-8 text-gray-500 mx-auto mb-2" />
                          <p className="text-gray-400 text-sm">
                            {usedGroupIds.size > 0
                              ? t('messages.allGroupsUsed') || 'All available groups have been used'
                              : t('messages.noGroupsWithContacts') || 'No groups with contacts available'
                            }
                          </p>
                        </div>
                      )
                    }

                    return availableGroups.map((group) => (
                      <button
                        key={group.id}
                        onClick={() => handleGroupSelect(group.id)}
                        className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 bg-gray-700/30 hover:bg-gray-600/50 rounded-xl transition-all duration-200 text-left space-y-2 sm:space-y-0"
                      >
                        <div className="flex items-center space-x-3">
                          <Users className="h-4 w-4 text-emerald-400 flex-shrink-0" />
                          <span className="text-white font-medium truncate">{group.name}</span>
                        </div>
                        <div className="flex items-center justify-between sm:justify-end space-x-2">
                          <span className="text-sm text-gray-400">
                            {group.contact_count || 0} {t('messages.people')}
                          </span>
                          <Plus className="h-4 w-4 text-emerald-400 flex-shrink-0" />
                        </div>
                      </button>
                    ))
                  })()}
                </div>
              </div>
            </div>
          )}

          {/* Recipients */}
          <div>
            <label className="block text-sm font-medium text-white mb-3">
              {t('messages.receivers')} *
            </label>

            {/* Add Receiver Section */}
            <div className="bg-gray-700/30 rounded-xl p-4 mb-4">
              <div className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-end">
                {/* Country Code */}
                <div className="w-full sm:w-32">
                  <label className="block text-xs font-medium text-gray-300 mb-2">
                    {t('auth.countryCode')}
                  </label>
                  <select
                    value={currentCountryCode}
                    onChange={(e) => setCurrentCountryCode(e.target.value)}
                    className="w-full px-3 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 appearance-none"
                  >
                    <option value="+90">🇹🇷 +90</option>
                    <option value="+1">🇺🇸 +1</option>
                    <option value="+44">🇬🇧 +44</option>
                    <option value="+49">🇩🇪 +49</option>
                    <option value="+33">🇫🇷 +33</option>
                    <option value="+39">🇮🇹 +39</option>
                    <option value="+34">🇪🇸 +34</option>
                    <option value="+31">🇳🇱 +31</option>
                    <option value="+7">🇷🇺 +7</option>
                    <option value="+86">🇨🇳 +86</option>
                    <option value="+81">🇯🇵 +81</option>
                    <option value="+82">🇰🇷 +82</option>
                    <option value="+91">🇮🇳 +91</option>
                    <option value="+55">🇧🇷 +55</option>
                    <option value="+52">🇲🇽 +52</option>
                    <option value="+61">🇦🇺 +61</option>
                    <option value="+27">🇿🇦 +27</option>
                    <option value="+20">🇪🇬 +20</option>
                    <option value="+966">🇸🇦 +966</option>
                    <option value="+971">🇦🇪 +971</option>
                  </select>
                </div>

                {/* Phone Number */}
                <div className="flex-1">
                  <label className="block text-xs font-medium text-gray-300 mb-2">
                    {t('auth.phone')}
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="tel"
                      value={currentPhoneNumber}
                      onChange={(e) => setCurrentPhoneNumber(e.target.value)}
                      placeholder={t('auth.phonePlaceholder') ?? ''}
                      className="block w-full pl-10 pr-3 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>

                {/* Buttons */}
                <div className="flex gap-2">
                  {/* Add/Save Button */}
                  <button
                    type="button"
                    onClick={editingReceiver ? saveEditReceiver : addReceiver}
                    className="flex-1 sm:flex-none px-4 sm:px-6 py-3 bg-emerald-600 text-white rounded-xl font-medium transition-colors duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800 flex items-center justify-center space-x-2"
                  >
                    <Plus size={16} />
                    <span className="hidden sm:inline">{editingReceiver ? t('common.save') : t('messages.addReceiver')}</span>
                    <span className="sm:hidden">{editingReceiver ? t('common.save') : t('common.add')}</span>
                  </button>

                  {/* Cancel Edit Button */}
                  {editingReceiver && (
                    <button
                      type="button"
                      onClick={cancelEditReceiver}
                      className="px-4 py-3 bg-gray-600 text-white rounded-xl font-medium transition-colors duration-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
              </div>
              <p className="mt-2 text-xs text-gray-400">
                {t('messages.phoneNumberHint')}
              </p>
            </div>

            {/* Added Receivers List */}
            <div className="bg-gray-700/30 rounded-xl p-4">
              <h4 className="text-sm font-medium text-white mb-3">
                {t('messages.addedReceivers')} ({receiversList.length})
              </h4>

              {receiversList.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400 text-sm">{t('messages.noReceiversAdded')}</p>
                </div>
              ) : (
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {receiversList.map((receiver) => (
                    <div
                      key={receiver.id}
                      className="flex items-center justify-between bg-gray-600/50 rounded-lg p-3"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-emerald-600 rounded-full flex items-center justify-center">
                          <Phone size={14} className="text-white" />
                        </div>
                        <div>
                          <p className="text-white font-medium">{receiver.fullNumber}</p>
                          <p className="text-gray-400 text-xs">
                            {receiver.countryCode} {receiver.phoneNumber}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          type="button"
                          onClick={() => startEditReceiver(receiver)}
                          className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded-lg transition-colors duration-200"
                          title={t('messages.editReceiver') ?? ''}
                        >
                          <Edit2 size={14} />
                        </button>
                        <button
                          type="button"
                          onClick={() => removeReceiver(receiver.id)}
                          className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-900/20 rounded-lg transition-colors duration-200"
                          title={t('messages.removeReceiver') ?? ''}
                        >
                          <X size={14} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Device Selection */}
          <div>
            <label className="block text-sm font-medium text-white mb-3">
              {t('messages.deviceSelection')} *
            </label>
            <select
              className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              required
            >
              <option value="" className="bg-gray-700">{t('messages.selectDevice')}</option>
              {devices && devices.map((device) => (
                <option key={device.registration_id} value={device.registration_id} className="bg-gray-700">
                  {device.business_name || device.device_number}
                  {device.state === '1' ? ` (${t('common.online')})` : ` (${t('common.offline')})`}
                </option>
              ))}
            </select>
            {(!devices || devices.length === 0) && (
              <p className="mt-2 text-xs text-red-400">
                {t('messages.noDevices')}
              </p>
            )}
          </div>

          {/* Send Time */}
          <div>
            <label htmlFor="sendTime" className="block text-sm font-medium text-white mb-3">
              {t('messages.sendTime')} ({t('common.optional')})
            </label>
            <input
              type="datetime-local"
              id="sendTime"
              className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
              value={sendTime}
              onChange={(e) => setSendTime(e.target.value)}
            />
            <p className="mt-2 text-xs text-gray-400">
              {t('messages.sendTimeHint')}
            </p>
          </div>

          {/* Message Type */}
          <div>
            <label className="block text-sm font-medium text-white mb-3">
              {t('messages.messageType')}
            </label>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              {[
                { value: '1', label: t('messages.text'), icon: FileText },
                { value: '2', label: t('messages.media'), icon: Upload },
                { value: '3', label: t('messages.poll'), icon: BarChart3 }
              ].map((option) => {
                const Icon = option.icon
                return (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => setFormat(option.value)}
                    className={cn(
                      "flex items-center justify-center space-x-2 px-4 py-3 rounded-xl transition-all duration-200 flex-1 sm:flex-none",
                      format === option.value
                        ? "bg-emerald-500/20 text-white shadow-lg shadow-emerald-500/10 border border-emerald-500/30"
                        : "bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white"
                    )}
                  >
                    <Icon size={16} />
                    <span>{option.label}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Message Content */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-white mb-3">
              {t('messages.content')} *
            </label>
            <textarea
              id="content"
              rows={6}
              className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
              placeholder={t('messages.enterMessage') ?? ''}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              required
            />
          </div>

          {/* Media Upload - Only show when format is '2' */}
          {format === '2' && (
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                {t('messages.fileUpload')} *
              </label>
              <div className="relative">
                <input
                  type="file"
                  id="fileUpload"
                  className="hidden"
                  onChange={handleFileChange}
                  accept="image/*,video/*,audio/*,application/pdf,.doc,.docx"
                />
                <label
                  htmlFor="fileUpload"
                  className="flex items-center justify-center w-full px-4 py-6 bg-gray-700/50 rounded-xl border-2 border-dashed border-gray-600 hover:border-emerald-500 transition-all duration-200 cursor-pointer"
                >
                  <div className="text-center">
                    <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-300">
                      {selectedFile ? selectedFile.name : t('messages.selectFile')}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {t('messages.supportedFormats') || 'Images, videos, audio, documents'}
                    </p>
                  </div>
                </label>
              </div>
            </div>
          )}

          {/* Poll Options - Only show when format is '3' */}
          {format === '3' && (
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                {t('messages.pollOptions')} *
              </label>
              <div className="space-y-3">
                {pollOptions.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => updatePollOption(index, e.target.value)}
                      placeholder={`${t('messages.pollOptionPlaceholder')} ${index + 1}`}
                      className="flex-1 px-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                    />
                    {pollOptions.length > 2 && (
                      <button
                        type="button"
                        onClick={() => removePollOption(index)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-200"
                        title={t('messages.removePollOption') ?? ''}
                      >
                        <Trash2 size={16} />
                      </button>
                    )}
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addPollOption}
                  className="flex items-center space-x-2 px-4 py-2 text-emerald-400 hover:text-emerald-300 hover:bg-emerald-500/10 rounded-lg transition-all duration-200"
                >
                  <Plus size={16} />
                  <span>{t('messages.addPollOption')}</span>
                </button>

                {/* Selectable Options Count */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-white mb-2">
                    {t('messages.selectableCount')} *
                  </label>
                  <input
                    type="number"
                    min="1"
                    max={pollOptions.filter(opt => opt.trim() !== '').length || 1}
                    value={selectableOptionCount}
                    onChange={(e) => setSelectableOptionCount(parseInt(e.target.value) || 1)}
                    placeholder={t('messages.selectableCountPlaceholder') ?? ''}
                    className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className={cn(
                "flex items-center space-x-2 px-8 py-3 bg-emerald-600 text-white rounded-xl font-medium transition-all duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800 shadow-lg shadow-emerald-500/25",
                isLoading && "opacity-50 cursor-not-allowed"
              )}
            >
              <Send size={16} />
              <span>{isLoading ? t('messages.sending') : t('messages.send')}</span>
            </button>
          </div>
        </form>
      </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          {[
            {
              title: t('messages.totalGroups'),
              value: groups.length.toString(),
              icon: Users,
              color: 'text-blue-500',
              bgColor: 'bg-blue-500/10',
            },
            {
              title: t('messages.activeDevices'),
              value: devices.filter(d => d.state === '1').length.toString(),
              icon: Smartphone,
              color: 'text-purple-500',
              bgColor: 'bg-purple-500/10',
            },
            {
              title: t('messages.sentToday'),
              value: sentToday.toString(),
              icon: Send,
              color: 'text-emerald-500',
              bgColor: 'bg-emerald-500/10',
            },
            {
              title: t('messages.scheduled'),
              value: scheduled.toString(),
              icon: Calendar,
              color: 'text-orange-500',
              bgColor: 'bg-orange-500/10',
            }
          ].map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-400">{stat.title}</p>
                    <p className="text-3xl font-bold text-white">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Credit Exhausted Modal */}
      <ConfirmationModal
        isOpen={showCreditModal}
        onClose={() => setShowCreditModal(false)}
        onConfirm={handleGoToPackages}
        title={t('messages.creditExhausted') ?? ''}
        message={t('messages.purchaseMessagePackage') ?? ''}
        confirmText={t('messages.goToPackages') ?? ''}
        type="info"
        icon={<ShoppingCart className="h-6 w-6" />}
      />
    </div>
  )
}
