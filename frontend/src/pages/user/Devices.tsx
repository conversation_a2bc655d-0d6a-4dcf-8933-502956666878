import React, { useState, useEffect, useCallback } from 'react'
import { Smartphone, QrCode, Trash2, RefreshCw, CheckCircle, XCircle, Hash, X, ShoppingCart } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useNotification } from '../../contexts/NotificationContext'
import { deviceApi } from '../../lib/api'
import { cn } from '../../lib/utils'
import { Table, TableColumn } from '../../components/Table'

interface Device {
  j_id: string
  registration_id: string
  platform: string
  push_name: string
  business_name: string
  device_number: string
  state: string // 1- online 2- çıkış
}

export const Devices: React.FC = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { showSuccess, showError } = useNotification()
  const [devices, setDevices] = useState<Device[]>([])
  const [qrCodeImage, setQrCodeImage] = useState('')
  const [showQrModal, setShowQrModal] = useState(false)
  const [showAddDeviceModal, setShowAddDeviceModal] = useState(false)
  const [showCodeModal, setShowCodeModal] = useState(false)
  const [showLimitModal, setShowLimitModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isGeneratingQr, setIsGeneratingQr] = useState(false)
  const [isGettingCode, setIsGettingCode] = useState(false)
  const [countryCode, setCountryCode] = useState('+90')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [code, setCode] = useState('')
  const [regId, setRegId] = useState('')
  const [qrCountdown, setQrCountdown] = useState(0)
  const [codeCountdown, setCodeCountdown] = useState(0)
  const [qrExpired, setQrExpired] = useState(false)
  const [codeExpired, setCodeExpired] = useState(false)
  const [isCheckingDevice, setIsCheckingDevice] = useState(false)

  // Table columns definition
  const columns: TableColumn<Device>[] = [
    {
      key: 'device_info',
      title: t('devices.device') || 'Device',
      render: (_, device) => (
        <div className="flex items-center">
          <Smartphone className="h-5 w-5 text-emerald-400 mr-3" />
          <div>
            <div className="text-sm font-medium text-white">
              {device.business_name || device.push_name || t('devices.whatsappDevice') || 'WhatsApp Device'}
            </div>
            <div className="text-xs text-gray-400">
              {device.platform || 'Unknown Platform'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'device_number',
      title: t('auth.phone'),
      sortable: true,
      render: (value) => (
        <span className="text-gray-300">{value}</span>
      )
    },
    {
      key: 'state',
      title: t('messages.status'),
      sortable: true,
      render: (value) => (
        <div className="flex items-center">
          <div className="mr-2">
            {getStatusIcon(value)}
          </div>
          <span className={`text-sm font-medium ${getStatusColor(value).split(' ')[1]}`}>
            {getStatusText(value)}
          </span>
        </div>
      )
    },
    {
      key: 'registration_id',
      title: t('devices.registrationId') || 'Registration ID',
      render: (value) => (
        <span className="text-xs text-gray-400 font-mono">{value}</span>
      )
    },
    {
      key: 'actions',
      title: t('messages.actions'),
      className: 'text-right',
      render: (_, device) => (
        <button
          onClick={() => handleLogout(device.registration_id)}
          className="text-red-400 hover:text-red-300 transition-colors duration-200 p-2 rounded-lg hover:bg-red-500/10"
          title={t('devices.removeDevice') || 'Remove device'}
        >
          <Trash2 size={16} />
        </button>
      )
    }
  ]

  const fetchDevices = useCallback(async (signal?: AbortSignal) => {
    try {
      setIsLoading(true)
      const data = await deviceApi.getDevices()
      if (signal?.aborted) return
      setDevices(data || [])
    } catch (err: any) {
      if (signal?.aborted) return
      showError(t('notifications.loadError'))
      console.error('Error fetching devices:', err)
      setDevices([])
    } finally {
      if (!signal?.aborted) {
        setIsLoading(false)
      }
    }
  }, [showError, t])

  const closeQrModal = useCallback(() => {
    setShowQrModal(false)
    setQrCodeImage('')
    setRegId('')
    setQrCountdown(0)
    setQrExpired(false)
  }, [])

  const checkDeviceConnection = useCallback(async (regId: string, isQr: boolean = true) => {
    try {
      setIsCheckingDevice(true)
      const response = await deviceApi.checkDevice(regId)

      if (response.is_connected) {
        // Device başarıyla bağlandı
        showSuccess(t('notifications.connectionSuccess'))

        // Modal'ları kapat
        if (isQr) {
          closeQrModal()
        } else {
          setShowCodeModal(false)
          setCountryCode('+90')
          setPhoneNumber('')
          setCode('')
          setCodeCountdown(0)
          setCodeExpired(false)
          setRegId('')
        }

        // Device listesini yenile
        fetchDevices()
      }
    } catch (err: any) {
      console.error('Device check error:', err)
      // Timeout veya hata durumunda tekrar QR/Code üretilmesini iste
      if (isQr) {
        setQrExpired(true)
        setQrCountdown(0)
      } else {
        setCodeExpired(true)
        setCodeCountdown(0)
      }
    } finally {
      setIsCheckingDevice(false)
    }
  }, [showSuccess, t, closeQrModal, fetchDevices])

  useEffect(() => {
    const abortController = new AbortController()

    fetchDevices(abortController.signal)

    return () => {
      abortController.abort()
    }
  }, [fetchDevices])

  // QR countdown effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (qrCountdown > 0) {
      interval = setInterval(() => {
        setQrCountdown(prev => {
          if (prev <= 1) {
            setQrExpired(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [qrCountdown])

  // Code countdown effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (codeCountdown > 0) {
      interval = setInterval(() => {
        setCodeCountdown(prev => {
          if (prev <= 1) {
            setCodeExpired(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [codeCountdown])

  // QR device check effect
  useEffect(() => {
    let checkInterval: NodeJS.Timeout
    if (regId && showQrModal && !qrExpired && !isCheckingDevice) {
      // QR üretildikten sonra device check'i başlat
      checkInterval = setInterval(() => {
        checkDeviceConnection(regId, true)
      }, 3000) // Her 3 saniyede bir kontrol et
    }
    return () => clearInterval(checkInterval)
  }, [regId, showQrModal, qrExpired, isCheckingDevice, checkDeviceConnection])

  // Code device check effect
  useEffect(() => {
    let checkInterval: NodeJS.Timeout
    if (regId && showCodeModal && code && !codeExpired && !isCheckingDevice) {
      // Code üretildikten sonra device check'i başlat
      checkInterval = setInterval(() => {
        checkDeviceConnection(regId, false)
      }, 3000) // Her 3 saniyede bir kontrol et
    }
    return () => clearInterval(checkInterval)
  }, [regId, showCodeModal, code, codeExpired, isCheckingDevice, checkDeviceConnection])



  const generateQrCode = async () => {
    try {
      setIsGeneratingQr(true)
      setQrExpired(false)

      const data = await deviceApi.getQr()
      setRegId(data.reg_id)

      // QR string'ini görsel QR kod'a dönüştür (Google Charts API kullanarak)
      const qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(data.qr)}`
      setQrCodeImage(qrImageUrl)

      // 30 saniyelik geri sayımı başlat
      setQrCountdown(30)

      setShowQrModal(true)
      setShowAddDeviceModal(false)

      // QR üretildikten sonra device check başlayacak (useEffect ile)

    } catch (err: any) {
      if (err.response?.data?.device_limit_exceed) {
        setShowAddDeviceModal(false)
        setShowLimitModal(true)
      } else {
        showError(t('notifications.operationError'))
      }
    } finally {
      setIsGeneratingQr(false)
    }
  }

  const getCode = async () => {
    if (!phoneNumber.trim()) {
      showError(t('notifications.validationError'))
      return
    }

    try {
      setIsGettingCode(true)
      setCodeExpired(false)

      // Ülke kodu ve telefon numarasını birleştir
      const fullPhoneNumber = countryCode + phoneNumber
      const data = await deviceApi.getCode(fullPhoneNumber)
      setRegId(data.regId)
      setCode(data.code) // Backend'den gelen kodu set et

      // 30 saniyelik geri sayımı başlat
      setCodeCountdown(30)

      setShowCodeModal(true)
      setShowAddDeviceModal(false)

      // Code üretildikten sonra device check başlayacak (useEffect ile)

    } catch (err: any) {
      if (err.response?.data?.device_limit_exceed) {
        setShowAddDeviceModal(false)
        setShowCodeModal(false)
        setShowLimitModal(true)
      } else {
        showError(t('notifications.operationError'))
      }
    } finally {
      setIsGettingCode(false)
    }
  }

  const handleLogout = async (regId: string) => {
    if (!window.confirm(t('devices.logoutConfirm') || 'Are you sure you want to force logout this device?')) {
      return
    }

    try {
      await deviceApi.logout(regId)
      showSuccess(t('notifications.disconnectionSuccess'))
      fetchDevices()
    } catch (err: any) {
      showError(t('notifications.disconnectionError'))
    }
  }

  const getStatusIcon = (state: string) => {
    switch (state) {
      case '1':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case '2':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <XCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = (state: string) => {
    switch (state) {
      case '1': return t('common.online')
      case '2': return t('common.offline')
      default: return t('messages.unknown') || 'Unknown'
    }
  }

  const getStatusColor = (state: string) => {
    switch (state) {
      case '1': return 'bg-emerald-500/10 text-emerald-400'
      case '2': return 'bg-red-500/10 text-red-400'
      default: return 'bg-gray-500/10 text-gray-400'
    }
  }

  if (isLoading) {
    return (
      <div className="h-full bg-gray-900 text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"></div>
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white">
      <div className="h-full px-4 md:px-6 py-4 md:py-8 space-y-6 md:space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
          <div className="space-y-1 md:space-y-2">
            <h1 className="text-2xl md:text-3xl font-bold text-emerald-400">
              {t('navigation.devices')}
            </h1>
            <p className="text-gray-400 text-base md:text-lg">
              {t('devices.description')}
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="flex items-center space-x-2 bg-gray-800 px-3 md:px-4 py-2 rounded-lg">
              <Smartphone className="h-4 w-4 text-emerald-400" />
              <span className="text-sm text-gray-300">{t('navigation.devices')}</span>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => fetchDevices()}
                className="flex items-center justify-center space-x-2 px-3 md:px-4 py-2 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-colors duration-200 flex-1 sm:flex-none"
              >
                <RefreshCw size={16} />
                <span className="hidden sm:inline">{t('common.refresh')}</span>
              </button>

              <button
                onClick={() => setShowAddDeviceModal(true)}
                className="flex items-center justify-center space-x-2 px-4 md:px-6 py-2 md:py-3 bg-emerald-600 text-white rounded-xl font-medium transition-all duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800 shadow-lg shadow-emerald-500/25 flex-1 sm:flex-none"
              >
                <Smartphone size={16} />
                <span className="hidden sm:inline">{t('devices.addDevice') || 'Add New Device'}</span>
                <span className="sm:hidden">{t('common.add') || 'Add'}</span>
              </button>
            </div>
          </div>
        </div>



        {/* Devices List */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl overflow-hidden">
          <div className="p-4 md:p-6">
            <h3 className="text-lg md:text-xl font-bold text-white truncate">
              {t('devices.connectedDevices') || 'Connected Devices'} ({devices.length})
            </h3>
          </div>

          <div className="w-full">
            <Table
              data={devices}
              columns={columns}
              loading={isLoading}
              emptyIcon={Smartphone}
              emptyTitle={t('devices.noDevices') || 'No devices found'}
              emptyDescription={t('devices.noDevicesDescription') || 'No connected devices yet. Generate QR code to add new device.'}
              pageSize={10}
              showPagination={true}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6">
        <div className="bg-gray-800/50 backdrop-blur-sm p-4 md:p-6 rounded-2xl">
          <div className="flex items-center justify-between">
            <div className="space-y-1 md:space-y-2 min-w-0 flex-1">
              <p className="text-xs md:text-sm font-medium text-gray-400 truncate">{t('devices.activeDevices') || 'Active Devices'}</p>
              <p className="text-2xl md:text-3xl font-bold text-white">
                {(devices || []).filter(d => d.state === '1').length}
              </p>
            </div>
            <div className="p-2 md:p-3 bg-emerald-600 rounded-xl flex-shrink-0">
              <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm p-4 md:p-6 rounded-2xl">
          <div className="flex items-center justify-between">
            <div className="space-y-1 md:space-y-2 min-w-0 flex-1">
              <p className="text-xs md:text-sm font-medium text-gray-400 truncate">{t('devices.inactiveDevices') || 'Inactive Devices'}</p>
              <p className="text-2xl md:text-3xl font-bold text-white">
                {(devices || []).filter(d => d.state === '2').length}
              </p>
            </div>
            <div className="p-2 md:p-3 bg-red-600 rounded-xl flex-shrink-0">
              <XCircle className="h-5 w-5 md:h-6 md:w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm p-4 md:p-6 rounded-2xl">
          <div className="flex items-center justify-between">
            <div className="space-y-1 md:space-y-2 min-w-0 flex-1">
              <p className="text-xs md:text-sm font-medium text-gray-400 truncate">{t('devices.totalDevices') || 'Total Devices'}</p>
              <p className="text-2xl md:text-3xl font-bold text-white">{(devices || []).length}</p>
            </div>
            <div className="p-2 md:p-3 bg-blue-600 rounded-xl flex-shrink-0">
              <Smartphone className="h-5 w-5 md:h-6 md:w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* QR Code Modal */}
      {showQrModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 p-4">
          <div className="relative top-4 md:top-20 mx-auto p-4 md:p-5 w-full max-w-sm md:max-w-md shadow-lg rounded-2xl bg-gray-800">
            <div className="mt-3 text-center">
              <h3 className="text-lg font-medium text-white mb-4">{t('devices.qrTitle') || 'WhatsApp QR Code'}</h3>

              {qrExpired ? (
                <div className="space-y-4">
                  <div className="flex justify-center">
                    <div className="w-64 h-64 bg-gray-700 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <XCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
                        <p className="text-red-400 font-medium">{t('devices.qrExpired') || 'QR Code Expired'}</p>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-400">
                    {t('devices.qrExpiredMessage') || 'The QR code has expired. Please generate a new one.'}
                  </p>
                  <button
                    onClick={generateQrCode}
                    disabled={isGeneratingQr}
                    className={cn(
                      "w-full px-4 py-2 bg-emerald-600 text-white rounded-lg font-medium transition-colors duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800",
                      isGeneratingQr && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    {isGeneratingQr ? (t('devices.generatingQr') || 'Generating...') : (t('devices.generateNewQr') || 'Generate New QR')}
                  </button>
                </div>
              ) : qrCodeImage ? (
                <div className="space-y-4">
                  <div className="flex justify-center">
                    <img src={qrCodeImage} alt="QR Code" className="w-64 h-64" />
                  </div>
                  <p className="text-sm text-gray-400">
                    {t('devices.qrInstruction') || 'Scan this QR code with your WhatsApp app'}
                  </p>
                  {qrCountdown > 0 && (
                    <div className="flex items-center justify-center space-x-2">
                      <RefreshCw className="h-4 w-4 text-emerald-400" />
                      <span className="text-sm text-emerald-400">
                        {t('devices.expiresIn') || 'Expires in'}: {qrCountdown}s
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                </div>
              )}
              
              <div className="flex justify-center mt-6">
                <button
                  onClick={closeQrModal}
                  className="btn-secondary px-4 py-2"
                >
                  {t('common.close')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Device Modal */}
      {showAddDeviceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{t('devices.addDeviceTitle')}</h3>
              <button
                onClick={() => setShowAddDeviceModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            <p className="text-gray-300 mb-6">{t('devices.addDeviceDescription')}</p>

            <div className="space-y-4">
              <button
                onClick={generateQrCode}
                disabled={isGeneratingQr}
                className={cn(
                  "w-full flex items-center space-x-3 p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors duration-200",
                  isGeneratingQr && "opacity-50 cursor-not-allowed"
                )}
              >
                <QrCode size={20} className="text-emerald-400" />
                <div className="text-left">
                  <div className="font-medium text-white">{t('devices.addWithQr')}</div>
                  <div className="text-sm text-gray-400">{t('devices.qrMethodDescription')}</div>
                </div>
              </button>

              <button
                onClick={() => {
                  setShowAddDeviceModal(false)
                  setShowCodeModal(true)
                }}
                className="w-full flex items-center space-x-3 p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors duration-200"
              >
                <Hash size={20} className="text-blue-400" />
                <div className="text-left">
                  <div className="font-medium text-white">{t('devices.addWithCode')}</div>
                  <div className="text-sm text-gray-400">{t('devices.codeMethodDescription')}</div>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Code Modal */}
      {showCodeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{t('devices.codeMethod')}</h3>
              <button
                onClick={() => {
                  setShowCodeModal(false)
                  setCountryCode('+90')
                  setPhoneNumber('')
                  setCode('')
                  setCodeCountdown(0)
                  setCodeExpired(false)
                  setRegId('')
                }}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t('devices.phoneNumber')}
                </label>
                <div className="flex space-x-2">
                  {/* Ülke Kodu */}
                  <select
                    value={countryCode}
                    onChange={(e) => setCountryCode(e.target.value)}
                    className="w-24 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  >
                    <option value="+90">🇹🇷 +90</option>
                    <option value="+1">🇺🇸 +1</option>
                    <option value="+44">🇬🇧 +44</option>
                    <option value="+49">🇩🇪 +49</option>
                    <option value="+33">🇫🇷 +33</option>
                    <option value="+39">🇮🇹 +39</option>
                    <option value="+34">🇪🇸 +34</option>
                    <option value="+31">🇳🇱 +31</option>
                    <option value="+7">🇷🇺 +7</option>
                    <option value="+86">🇨🇳 +86</option>
                    <option value="+81">🇯🇵 +81</option>
                    <option value="+82">🇰🇷 +82</option>
                    <option value="+91">🇮🇳 +91</option>
                    <option value="+55">🇧🇷 +55</option>
                    <option value="+52">🇲🇽 +52</option>
                    <option value="+61">🇦🇺 +61</option>
                    <option value="+27">🇿🇦 +27</option>
                    <option value="+20">🇪🇬 +20</option>
                    <option value="+966">🇸🇦 +966</option>
                    <option value="+971">🇦🇪 +971</option>
                  </select>

                  {/* Telefon Numarası */}
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder={t('devices.phoneNumberPlaceholder') ?? ''}
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {t('devices.fullPhoneExample')}: {countryCode}5551234567
                </p>
              </div>

              {!regId && (
                <button
                  onClick={getCode}
                  disabled={isGettingCode || !phoneNumber.trim() || !countryCode}
                  className={cn(
                    "w-full px-4 py-2 bg-emerald-600 text-white rounded-lg font-medium transition-colors duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800",
                    (isGettingCode || !phoneNumber.trim() || !countryCode) && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {isGettingCode ? t('devices.gettingCode') : t('devices.getCode')}
                </button>
              )}

              {regId && (
                <div className="mt-4 p-4 bg-gray-700 rounded-lg">
                  {codeExpired ? (
                    <div className="text-center space-y-4">
                      <div className="flex items-center justify-center">
                        <XCircle className="h-8 w-8 text-red-400 mr-2" />
                        <span className="text-red-400 font-medium">{t('devices.codeExpired') || 'Code Expired'}</span>
                      </div>
                      <p className="text-sm text-gray-400">
                        {t('devices.codeExpiredMessage') || 'The verification code has expired. Please request a new one.'}
                      </p>
                      <button
                        onClick={getCode}
                        disabled={isGettingCode || !phoneNumber.trim()}
                        className={cn(
                          "w-full px-4 py-2 bg-emerald-600 text-white rounded-lg font-medium transition-colors duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800",
                          (isGettingCode || !phoneNumber.trim()) && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        {isGettingCode ? (t('devices.gettingCode') || 'Getting Code...') : (t('devices.getNewCode') || 'Get New Code')}
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="flex items-center justify-between mb-4">
                        <p className="text-sm text-gray-300">{t('devices.codeDisplayInstruction')}</p>
                        {codeCountdown > 0 && (
                          <div className="flex items-center space-x-1">
                            <RefreshCw className="h-3 w-3 text-emerald-400" />
                            <span className="text-xs text-emerald-400">
                              {codeCountdown}s
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Kodu göster */}
                      <div className="bg-gray-600 rounded-lg p-4 text-center">
                        <p className="text-xs text-gray-400 mb-2">{t('devices.verificationCode')}</p>
                        <div className="text-2xl font-mono font-bold text-white tracking-wider">
                          {code}
                        </div>
                        <p className="text-xs text-gray-400 mt-2">{t('devices.enterCodeInWhatsApp')}</p>
                      </div>

                      <div className="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                        <div className="flex items-start space-x-2">
                          <div className="flex-shrink-0 mt-0.5">
                            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs font-bold">!</span>
                            </div>
                          </div>
                          <div className="text-sm text-blue-300">
                            {t('devices.codeInstructions')}
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Device Limit Modal */}
      {showLimitModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{t('devices.deviceLimitExceeded')}</h3>
              <button
                onClick={() => setShowLimitModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            <div className="text-center">
              <div className="mb-4">
                <ShoppingCart size={48} className="mx-auto text-emerald-400 mb-3" />
                <p className="text-gray-300 mb-6">{t('devices.deviceLimitMessage')}</p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => {
                    setShowLimitModal(false)
                    navigate('/packages')
                  }}
                  className="w-full px-4 py-3 bg-emerald-600 text-white rounded-lg font-medium transition-all duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                >
                  <div className="flex items-center justify-center space-x-2">
                    <ShoppingCart size={16} />
                    <span>{t('devices.goToPackages')}</span>
                  </div>
                </button>

                <button
                  onClick={() => setShowLimitModal(false)}
                  className="w-full px-4 py-2 bg-gray-700 text-white rounded-lg font-medium transition-colors duration-200 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                >
                  {t('common.close')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
