import React, { useState, useEffect, useCallback } from 'react'
import { UserX, Plus, Trash2, Search } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useNotification } from '../../contexts/NotificationContext'
import { blacklistApi, BlacklistItem, BlacklistStats } from '../../lib/api'
import { CountryCodeSelectBlacklist } from '../../components/CountryCodeSelect'
import { Table, TableColumn } from '../../components/Table'
import { cn } from '../../lib/utils'
import ConfirmationModal from '../../components/ConfirmationModal'



export const Blacklist: React.FC = () => {
  const { t } = useTranslation()
  const { showSuccess, showError } = useNotification()
  const [blacklist, setBlacklist] = useState<BlacklistItem[]>([])
  const [stats, setStats] = useState<BlacklistStats>({
    total_blacklisted: 0,
    added_this_month: 0,
    removed_this_month: 0
  })
  const [newPhone, setNewPhone] = useState('')
  const [newCountryCode, setNewCountryCode] = useState('90')
  const [newReason, setNewReason] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<BlacklistItem | null>(null)

  const fetchBlacklist = useCallback(async () => {
    try {
      setIsLoading(true)
      const data = await blacklistApi.getBlacklist()
      setBlacklist(data || [])
    } catch (err: any) {
      console.error('Error fetching blacklist:', err)
      showError(err.response?.data?.message || t('notifications.loadError'))
      setBlacklist([])
    } finally {
      setIsLoading(false)
    }
  }, [showError, t])

  const fetchStats = useCallback(async () => {
    try {
      const data = await blacklistApi.getBlacklistStats()
      setStats(data)
    } catch (err: any) {
      console.error('Error fetching blacklist stats:', err)
      // Don't show error for stats, just log it
    }
  }, [])

  useEffect(() => {
    fetchBlacklist()
    fetchStats()
  }, [fetchBlacklist, fetchStats])

  const handleAddToBlacklist = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsAdding(true)

    try {
      if (!newPhone.trim()) {
        throw new Error(t('blacklist.validation.phoneRequired') ?? 'Phone number is required')
      }

      const data = {
        device_number: "default", // Cihaz numarası gerekli
        country_code: newCountryCode,
        number: newPhone.trim()
      }

      await blacklistApi.addToBlacklist(data)
      showSuccess(t('notifications.operationSuccess'))

      // Form'u temizle
      setNewPhone('')
      setNewCountryCode('90')
      setNewReason('')
      
      // Listeyi ve istatistikleri yenile
      fetchBlacklist()
      fetchStats()
      
    } catch (err: any) {
      showError(err.response?.data?.message || err.message || t('notifications.operationError'))
    } finally {
      setIsAdding(false)
    }
  }

  const handleRemoveFromBlacklist = (item: BlacklistItem) => {
    setItemToDelete(item)
    setShowDeleteConfirm(true)
  }

  const confirmRemoveFromBlacklist = async () => {
    if (!itemToDelete) return

    try {
      await blacklistApi.removeFromBlacklist(itemToDelete.id)
      showSuccess(t('notifications.operationSuccess'))
      fetchBlacklist()
      fetchStats()
    } catch (err: any) {
      showError(err.response?.data?.message || t('notifications.operationError'))
    }
  }

  const filteredBlacklist = (blacklist || []).filter(item =>
    item.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.device_number.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Table columns definition
  const columns: TableColumn<BlacklistItem>[] = [
    {
      key: 'number',
      title: t('auth.phone'),
      sortable: true,
      render: (value, item) => (
        <span className="font-medium text-white">
          {item.country_code && `+${item.country_code} `}{value}
        </span>
      )
    },
    {
      key: 'device_number',
      title: t('blacklist.device'),
      sortable: true,
      render: (value) => (
        <span className="text-gray-300">{value}</span>
      )
    },
    {
      key: 'created_at',
      title: t('blacklist.dateAdded'),
      sortable: true,
      render: (value) => (
        <span className="text-gray-300">
          {new Date(value).toLocaleDateString('tr-TR')}
        </span>
      )
    },
    {
      key: 'actions',
      title: t('blacklist.actions'),
      className: 'text-right',
      render: (_, item) => (
        <button
          onClick={() => handleRemoveFromBlacklist(item)}
          className="text-red-400 hover:text-red-300 transition-colors duration-200 p-2 rounded-lg hover:bg-red-500/10"
          title={t('blacklist.removeFromBlacklist') ?? ''}
        >
          <Trash2 size={16} />
        </button>
      )
    }
  ]

  return (
    <div className="h-full bg-gray-900 text-white">
      <div className="h-full px-4 md:px-6 py-4 md:py-8 space-y-6 md:space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
          <div className="space-y-1 md:space-y-2">
            <h1 className="text-2xl md:text-3xl font-bold text-emerald-400">
              {t('navigation.blacklist')}
            </h1>
            <p className="text-gray-400 text-base md:text-lg">
              {t('blacklist.description')}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2 bg-gray-800 px-3 md:px-4 py-2 rounded-lg">
              <UserX className="h-4 w-4 text-red-400" />
              <span className="text-sm text-gray-300">{t('navigation.blacklist')}</span>
            </div>
          </div>
        </div>

        {/* Add to Blacklist Form */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-4 md:p-6">
          <h3 className="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">{t('blacklist.addToBlacklist')}</h3>

        <form onSubmit={handleAddToBlacklist} className="space-y-4 md:space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-white mb-3">
                {t('auth.phone')} *
              </label>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <CountryCodeSelectBlacklist
                  value={newCountryCode}
                  onChange={setNewCountryCode}
                  className="w-full sm:w-32"
                />
                <input
                  type="text"
                  id="phone"
                  className="flex-1 px-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                  placeholder="5551234567"
                  value={newPhone}
                  onChange={(e) => setNewPhone(e.target.value)}
                  required
                />
              </div>
            </div>

            <div>
              <label htmlFor="reason" className="block text-sm font-medium text-white mb-3">
                {t('blacklist.reason')} ({t('common.optional')})
              </label>
              <input
                type="text"
                id="reason"
                className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                placeholder={t('blacklist.reasonPlaceholder') ?? ''}
                value={newReason}
                onChange={(e) => setNewReason(e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isAdding}
              className={cn(
                "flex items-center space-x-2 px-6 md:px-8 py-3 bg-emerald-600 text-white rounded-xl font-medium transition-all duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800 shadow-lg shadow-emerald-500/25",
                isAdding && "opacity-50 cursor-not-allowed"
              )}
            >
              <Plus size={16} />
              <span className="hidden sm:inline">{isAdding ? t('blacklist.adding') : t('blacklist.addToBlacklist')}</span>
              <span className="sm:hidden">{isAdding ? t('common.adding') : t('common.add')}</span>
            </button>
          </div>
        </form>
      </div>

        {/* Search and List */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl overflow-hidden">
          <div className="p-4 md:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h3 className="text-lg md:text-xl font-bold text-white truncate">{t('navigation.blacklist')} ({filteredBlacklist.length})</h3>

              <div className="relative w-full sm:w-auto sm:min-w-[300px]">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                  placeholder={t('blacklist.searchPlaceholder') ?? ''}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="w-full">
            <Table
              data={filteredBlacklist}
              columns={columns}
              loading={isLoading}
              emptyIcon={UserX}
              emptyTitle={t('blacklist.emptyTitle') ?? ''}
              emptyDescription={searchTerm ? (t('blacklist.noSearchResults') ?? '') : (t('blacklist.emptyDescription') ?? '')}
              pageSize={10}
              showPagination={true}
              className="w-full"
            />
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            {
              title: t('blacklist.totalBlacklisted'),
              value: stats.total_blacklisted.toString(),
              icon: UserX,
              color: 'text-red-500',
              bgColor: 'bg-red-500/10',
            },
            {
              title: t('blacklist.addedThisMonth'),
              value: stats.added_this_month.toString(),
              icon: Plus,
              color: 'text-blue-500',
              bgColor: 'bg-blue-500/10',
            },
            {
              title: t('blacklist.removedThisMonth'),
              value: stats.removed_this_month.toString(),
              icon: Trash2,
              color: 'text-emerald-500',
              bgColor: 'bg-emerald-500/10',
            }
          ].map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-400">{stat.title}</p>
                    <p className="text-3xl font-bold text-white">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmRemoveFromBlacklist}
        title={t('blacklist.removeFromBlacklist') ?? ''}
        message={t('blacklist.removeConfirm') ?? ''}
        confirmText={t('common.delete') ?? ''}
        type="danger"
      />
    </div>
  )
}
