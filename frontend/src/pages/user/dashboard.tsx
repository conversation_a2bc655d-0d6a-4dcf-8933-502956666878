import { useState, useEffect, useCallback } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts'
import { Calendar, TrendingUp, TrendingDown, MessageSquare, Users, Activity, Clock, CheckCircle, XCircle } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useNotification } from '../../contexts/NotificationContext'
import { messageApi } from '../../lib/api'

type TimeFilter = 'day' | 'month' | 'year'
type StatusFilter = 'all' | 'success' | 'failed'

interface DashboardStats {
  totalMessages: number
  successRate: number
  failedMessages: number
  successfulMessages: number
}

interface ChartData {
  name: string
  successful: number
  failed: number
  total: number
}



const cn = (...classes: string[]) => classes.filter(Boolean).join(' ')

export default function Dashboard() {
  const { t } = useTranslation()
  const { showError } = useNotification()
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('month')
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [chartData, setChartData] = useState<ChartData[]>([])

  const [stats, setStats] = useState<DashboardStats>({
    totalMessages: 0,
    successRate: 0,
    failedMessages: 0,
    successfulMessages: 0
  })

  const getTimeRange = useCallback(() => {
    const now = new Date()
    let startDate: Date

    switch (timeFilter) {
      case 'day':
        // Son 7 gün
        startDate = new Date(now)
        startDate.setDate(startDate.getDate() - 6)
        startDate.setHours(0, 0, 0, 0)
        break
      case 'month':
        // Son 6 ay
        startDate = new Date(now)
        startDate.setMonth(startDate.getMonth() - 5)
        startDate.setDate(1)
        startDate.setHours(0, 0, 0, 0)
        break
      case 'year':
        // Son 3 yıl
        startDate = new Date(now)
        startDate.setFullYear(startDate.getFullYear() - 2)
        startDate.setMonth(0, 1)
        startDate.setHours(0, 0, 0, 0)
        break
      default:
        startDate = new Date(now)
        startDate.setMonth(startDate.getMonth() - 5)
        startDate.setDate(1)
        startDate.setHours(0, 0, 0, 0)
    }

    return {
      initial_time: startDate.toISOString(),
      end_time: now.toISOString()
    }
  }, [timeFilter])

  const generateChartData = useCallback((successCount: number, failedCount: number, timeFilter: TimeFilter): ChartData[] => {
    const now = new Date()
    const dataMap = new Map<string, { successful: number; failed: number }>()

    // Initialize time periods based on filter
    switch (timeFilter) {
      case 'day':
        // Son 7 gün
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now)
          date.setDate(date.getDate() - i)
          const key = date.toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit' })
          dataMap.set(key, { successful: 0, failed: 0 })
        }
        break
      case 'month':
        // Son 6 ay
        for (let i = 5; i >= 0; i--) {
          const date = new Date(now)
          date.setMonth(date.getMonth() - i)
          const monthNames = ['january', 'february', 'march', 'april', 'may', 'june',
                             'july', 'august', 'september', 'october', 'november', 'december']
          const key = t(`dashboard.months.${monthNames[date.getMonth()]}`)
          dataMap.set(key, { successful: 0, failed: 0 })
        }
        break
      case 'year':
        // Son 3 yıl
        for (let i = 2; i >= 0; i--) {
          const year = now.getFullYear() - i
          const key = year.toString()
          dataMap.set(key, { successful: 0, failed: 0 })
        }
        break
    }

    // Distribute the total counts across the time periods
    // For simplicity, we'll put all data in the most recent period
    const keys = Array.from(dataMap.keys())
    if (keys.length > 0) {
      const mostRecentKey = keys[keys.length - 1]
      const current = dataMap.get(mostRecentKey)!
      current.successful = successCount
      current.failed = failedCount
    }

    return Array.from(dataMap.entries()).map(([name, data]) => ({
      name,
      successful: data.successful,
      failed: data.failed,
      total: data.successful + data.failed
    }))
  }, [t])

  const fetchData = useCallback(async (signal?: AbortSignal) => {
    try {
      setIsLoading(true)
      setError(null)

      // Fetch messages with filters
      const timeRange = getTimeRange()
      const statusParam = statusFilter === 'all' ? undefined :
                         statusFilter === 'success' ? '3' : '4'



      // Get dashboard stats
      const statsResponse = await messageApi.getDashboardStats({
        status: statusParam,
        initial_time: timeRange.initial_time,
        end_time: timeRange.end_time
      })

      // Check if request was aborted
      if (signal?.aborted) return

      // Use stats from backend response
      setStats({
        totalMessages: statsResponse.total_messages,
        successfulMessages: statsResponse.success_messages,
        failedMessages: statsResponse.failed_messages,
        successRate: statsResponse.total_messages > 0 ? Math.round((statsResponse.success_messages / statsResponse.total_messages) * 100) : 0
      })

      // Generate chart data from statistics only
      const generatedChartData = generateChartData(
        statsResponse.success_messages,
        statsResponse.failed_messages,
        timeFilter
      )
      setChartData(generatedChartData)

    } catch (err: any) {
      if (signal?.aborted) return
      const errorMessage = err.response?.data?.message || t('notifications.loadError')
      setError(errorMessage)
      showError(errorMessage)
      console.error('Error fetching data:', err)
    } finally {
      if (!signal?.aborted) {
        setIsLoading(false)
      }
    }
  }, [timeFilter, statusFilter, getTimeRange, t, showError, generateChartData])

  useEffect(() => {
    const abortController = new AbortController()
    fetchData(abortController.signal)
    return () => {
      abortController.abort()
    }
  }, [fetchData])

  const generateChartData = useCallback((successCount: number, failedCount: number, timeFilter: TimeFilter): ChartData[] => {
    const now = new Date()
    const dataMap = new Map<string, { successful: number; failed: number }>()

    // Initialize time periods based on filter
    switch (timeFilter) {
      case 'day':
        // Son 7 gün
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now)
          date.setDate(date.getDate() - i)
          const key = date.toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit' })
          dataMap.set(key, { successful: 0, failed: 0 })
        }
        break
      case 'month':
        // Son 6 ay
        for (let i = 5; i >= 0; i--) {
          const date = new Date(now)
          date.setMonth(date.getMonth() - i)
          const monthNames = ['january', 'february', 'march', 'april', 'may', 'june',
                             'july', 'august', 'september', 'october', 'november', 'december']
          const key = t(`dashboard.months.${monthNames[date.getMonth()]}`)
          dataMap.set(key, { successful: 0, failed: 0 })
        }
        break
      case 'year':
        // Son 3 yıl
        for (let i = 2; i >= 0; i--) {
          const year = now.getFullYear() - i
          const key = year.toString()
          dataMap.set(key, { successful: 0, failed: 0 })
        }
        break
    }

    // Distribute the total counts across the time periods
    // For simplicity, we'll put all data in the most recent period
    const keys = Array.from(dataMap.keys())
    if (keys.length > 0) {
      const mostRecentKey = keys[keys.length - 1]
      const current = dataMap.get(mostRecentKey)!
      current.successful = successCount
      current.failed = failedCount
    }

    return Array.from(dataMap.entries()).map(([name, data]) => ({
      name,
      successful: data.successful,
      failed: data.failed,
      total: data.successful + data.failed
    }))
  }, [t])

  // Pie chart data
  const pieData = [
    { name: t('dashboard.success'), value: stats.successfulMessages, color: '#10b981' },
    { name: t('dashboard.failed'), value: stats.failedMessages, color: '#ef4444' }
  ]

  if (isLoading) {
    return (
      <div className="h-full bg-gray-900 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-emerald-500 border-t-transparent"></div>
          <p className="text-gray-400 text-lg font-medium">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white">
      <div className="h-full px-4 md:px-6 py-4 md:py-8 space-y-6 md:space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
          <div className="space-y-1 md:space-y-2">
            <h1 className="text-2xl md:text-3xl font-bold text-emerald-400">
              {t('dashboard.title')}
            </h1>
            <p className="text-gray-400 text-base md:text-lg">
              {t('dashboard.subtitle')}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
            <div className="flex items-center space-x-2 bg-gray-800 px-3 md:px-4 py-2 rounded-lg">
              <Activity className="h-4 w-4 text-emerald-400" />
              <span className="text-sm text-gray-300">{t('dashboard.live')}</span>
            </div>
            <div className="flex items-center space-x-2 bg-gray-800 px-3 md:px-4 py-2 rounded-lg">
              <Clock className="h-4 w-4 text-blue-400" />
              <span className="text-sm text-gray-300">{new Date().toLocaleTimeString('tr-TR')}</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-800/50 backdrop-blur-sm p-4 md:p-6 rounded-2xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            {/* Time Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-300 mb-3">
                {t('dashboard.timeRange')}
              </label>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                {[
                  { value: 'day', label: t('dashboard.filterByDay'), icon: Calendar },
                  { value: 'month', label: t('dashboard.filterByMonth'), icon: Calendar },
                  { value: 'year', label: t('dashboard.filterByYear'), icon: Calendar }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setTimeFilter(option.value as TimeFilter)}
                    className={cn(
                      "flex items-center justify-center space-x-2 px-3 md:px-4 py-3 text-sm rounded-xl transition-all duration-200 font-medium flex-1 sm:flex-none",
                      timeFilter === option.value
                        ? "bg-emerald-600 text-white shadow-lg shadow-emerald-500/25"
                        : "bg-gray-700/50 text-gray-300 hover:bg-gray-700 hover:text-white"
                    )}
                  >
                    <option.icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{option.label}</span>
                    <span className="sm:hidden">{option.label.split(' ')[0]}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-300 mb-3">
                {t('dashboard.messageStatus')}
              </label>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                {[
                  { value: 'all', label: t('dashboard.all'), icon: MessageSquare },
                  { value: 'success', label: t('dashboard.success'), icon: CheckCircle },
                  { value: 'failed', label: t('dashboard.failed'), icon: XCircle }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setStatusFilter(option.value as StatusFilter)}
                    className={cn(
                      "flex items-center justify-center space-x-2 px-3 md:px-4 py-3 text-sm rounded-xl transition-all duration-200 font-medium flex-1 sm:flex-none",
                      statusFilter === option.value
                        ? "bg-blue-600 text-white shadow-lg shadow-blue-500/25"
                        : "bg-gray-700/50 text-gray-300 hover:bg-gray-700 hover:text-white"
                    )}
                  >
                    <option.icon className="h-4 w-4" />
                    <span>{option.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/50 rounded-xl p-4 backdrop-blur-sm">
            <p className="text-red-300 font-medium">{error}</p>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6">
          {[
            {
              title: t('dashboard.totalMessages'),
              value: stats.totalMessages.toLocaleString(),
              icon: MessageSquare,
              color: 'text-blue-500',
              bgColor: 'bg-blue-500/10',
            },
            {
              title: t('dashboard.successfulMessages'),
              value: stats.successfulMessages.toLocaleString(),
              icon: TrendingUp,
              color: 'text-emerald-500',
              bgColor: 'bg-emerald-500/10',
            },
            {
              title: t('dashboard.failedMessages'),
              value: stats.failedMessages.toLocaleString(),
              icon: TrendingDown,
              color: 'text-red-500',
              bgColor: 'bg-red-500/10',
            },
            {
              title: t('dashboard.successRate'),
              value: `%${stats.successRate}`,
              icon: Users,
              color: 'text-purple-500',
              bgColor: 'bg-purple-500/10',
            }
          ].map((stat, index) => (
            <div key={index} className="bg-gray-800/50 backdrop-blur-sm p-4 md:p-6 rounded-2xl transition-all duration-200">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-gray-400 text-xs md:text-sm font-medium mb-1 truncate">{stat.title}</p>
                  <p className="text-2xl md:text-3xl font-bold text-white mb-2">{stat.value}</p>
                </div>
                <div className={cn("p-3 md:p-4 rounded-2xl flex-shrink-0", stat.bgColor)}>
                  <stat.icon className={cn("h-6 w-6 md:h-8 md:w-8", stat.color)} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 md:gap-6">
          {/* Bar Chart */}
          <div className="xl:col-span-2 bg-gray-800/50 backdrop-blur-sm p-4 md:p-6 rounded-2xl">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 md:mb-6 space-y-2 sm:space-y-0">
              <h3 className="text-lg md:text-xl font-bold text-white">
                {t('dashboard.monthlyTrend')}
              </h3>
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                  <span>{t('dashboard.success')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>{t('dashboard.failed')}</span>
                </div>
              </div>
            </div>
            <div className="h-64 md:h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData} margin={{ top: 20, right: 10, left: 10, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis
                    dataKey="name"
                    stroke="#9CA3AF"
                    fontSize={12}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    stroke="#9CA3AF"
                    fontSize={12}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip
                    cursor={{ fill: 'rgba(55, 65, 81, 0.1)' }}
                    contentStyle={{
                      backgroundColor: '#1F2937',
                      border: '1px solid #374151',
                      borderRadius: '12px',
                      color: '#F9FAFB',
                      fontSize: '14px'
                    }}
                  />
                  <Bar dataKey="successful" fill="#10b981" name={t('dashboard.success') ?? ''} radius={[4, 4, 0, 0]} />
                  <Bar dataKey="failed" fill="#ef4444" name={t('dashboard.failed') ?? ''} radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Pie Chart */}
          <div className="bg-gray-800/50 backdrop-blur-sm p-4 md:p-6 rounded-2xl">
            <h3 className="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">{t('dashboard.successDistribution')}</h3>
            <div className="h-48 md:h-64 flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1F2937',
                      border: '1px solid #374151',
                      borderRadius: '12px',
                      color: '#F9FAFB',
                      fontSize: '14px'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="space-y-2 mt-4">
              {pieData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 min-w-0">
                    <div className="w-3 h-3 rounded-full flex-shrink-0" style={{ backgroundColor: item.color }}></div>
                    <span className="text-gray-300 text-sm truncate">{item.name}</span>
                  </div>
                  <span className="text-white font-medium text-sm flex-shrink-0">{item.value.toLocaleString()}</span>
                </div>
              ))}
            </div>
          </div>
        </div>


      </div>
    </div>
  )
}