import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import { useAuth } from './AuthContext'
import { userApi } from '../lib/api'

interface LanguageContextType {
  language: string
  changeLanguage: (lang: string) => void
  availableLanguages: { code: string; name: string }[]
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

interface LanguageProviderProps {
  children: ReactNode
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const { i18n } = useTranslation()
  const { user } = useAuth()
  const [language, setLanguage] = useState<string>('tr')

  const availableLanguages = [
    { code: 'tr', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { code: 'en', name: 'English' }
  ]

  // Initialize language from user preference or localStorage
  useEffect(() => {
    let initialLanguage = 'tr' // default

    // First priority: user's saved language preference
    if (user?.language) {
      initialLanguage = user.language
    } else {
      // Second priority: localStorage
      const savedLanguage = localStorage.getItem('language')
      if (savedLanguage && availableLanguages.some(lang => lang.code === savedLanguage)) {
        initialLanguage = savedLanguage
      }
    }

    setLanguage(initialLanguage)
    i18n.changeLanguage(initialLanguage)
  }, [user, i18n, availableLanguages])

  const changeLanguage = async (lang: string) => {
    if (availableLanguages.some(l => l.code === lang)) {
      setLanguage(lang)
      await i18n.changeLanguage(lang)
      localStorage.setItem('language', lang)

      // Update user's language preference in backend
      if (user) {
        try {
          await userApi.updateLanguage(lang)
        } catch (error) {
          console.error('Failed to update language preference:', error)
        }
      }
    }
  }

  const value: LanguageContextType = {
    language,
    changeLanguage,
    availableLanguages
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}
